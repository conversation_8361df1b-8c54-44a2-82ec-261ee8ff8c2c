const eventBus = require('utils/eventBus.js')
const log = require('./utils/logger.js')
const http = require('utils/http.js')
App({
  eventBus: eventBus,
  onLaunch: function () {
    // wx.login({
    //   success: (res) => {
    //     getApp().globalData.code = res.code
    //     // 判断是否已经登录
    //     const token = wx.getStorageSync('token')
    //     if (!token) {
    //       wx.redirectTo({
    //         url: '/pages/login/login'
    //       })
    //       return false
    //     }
    //     getApp().globalData.loginStatus = 1
    //     // 获取用户信息
    //     this.getUserInfoAsync()
    //   }
    // })
  },

  async getUserInfoAsync() {
    try {
      // 获取用户基本信息
      const userInfo = await http.requestPromise({
        url: '/app-api/member/user/get',
        method: 'GET'
      })

      // "groupId": 1 // 用户分组编号,1 学生家长 2 教师
      const groupId = userInfo.groupId || 0
      getApp().globalData.userInfo = userInfo
      getApp().globalData.groupId = groupId
      wx.setStorageSync('userInfo', JSON.stringify(userInfo))
      wx.setStorageSync('groupId', groupId)

      // 获取学生家长信息
      if (groupId === 1) {
        const studentParent = await http.requestPromise({
          url: '/app-api/member/student-parent/get-by-user-id',
          method: 'GET',
          data: {
            userId: wx.getStorageSync('userId')
          }
        })
        wx.setStorageSync('studentParent', JSON.stringify(studentParent))
        getApp().globalData.studentParent = studentParent
      } else if (groupId === 2) {
        const teacherInfo = await http.requestPromise({
          url: '/app-api/member/teacher-info/get',
          method: 'GET',
          data: {
            userId: wx.getStorageSync('userId')
          }
        })
        wx.setStorageSync('teacherInfo', JSON.stringify(teacherInfo))
        getApp().globalData.teacherInfo = teacherInfo
      }

      // 通知tabBar更新
      this.eventBus.emit('groupIdChange', groupId)

      // 跳转到首页
      if (groupId === 1) {
        // 学生家长
        wx.reLaunch({
          url: '/pages/index/index'
        })
      } else if (groupId === 2) {
        // 教师
        wx.reLaunch({
          url: '/pages/index-teacher/index-teacher'
        })
      } else {
        wx.reLaunch({
          url: '/pages/createInfo/createInfo'
        })
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 获取用户信息失败，可能是token过期，跳转到登录页
      wx.showToast({
        title: '登录状态已过期，请重新登录',
        icon: 'none',
        duration: 2000
      })
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/login/login'
        })
      }, 2000)
    }
  },

  globalData: {
    wxLoginInfo: {},
    loginStatus: 0, // 0 未登录 1 已登录
    code: '', // 登录code
    userId: '', // 用户id
    userInfo: {}, // 用户信息
    teacherInfo: {}, // 老师信息
    studentParent: {}, // 学生家长信息
    groupId: 1, //用户分组编号,1 学生家长 2 教师
    openid: '' // 微信openid
  }
})
