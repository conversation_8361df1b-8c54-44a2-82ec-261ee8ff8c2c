/*
 * @Author: 逗逗飞 <EMAIL>
 * @Date: 2025-05-25 15:50:27
 * @LastEditors: 逗逗飞 <EMAIL>
 * @LastEditTime: 2025-07-02 17:33:13
 * @Description: 用户登录
 */
const http = require('../../utils/http.js')
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户名
    userName: '',
    // 密码
    password: '',
    checked: false
  },

  goToWebSite(e) {
    console.log(e)
    const type = e.target.dataset.type
    wx.reLaunch({
      url: `/pages/webview/webview?target=${type}`
    })
  },

  handleChangeCheckbox(val) {
    if (val.detail.value.length > 0) {
      this.setData({ checked: true })
    } else {
      this.setData({ checked: false })
    }
    console.log(val)
  },

  getrealtimephonenumber(e) {
    if (!this.data.checked) {
      wx.showToast({
        title: '请先阅读协议',
        icon: 'none'
      })
      return
    }

    // 先获取微信登录 code
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 获取到 code 后进行登录
          this.performLogin(e.detail.code, loginRes.code)
        } else {
          wx.showToast({
            title: '获取登录凭证失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.showToast({
          title: '获取登录凭证失败',
          icon: 'none'
        })
      }
    })
  },

  performLogin(phoneCode, loginCode) {
    http.request(
      {
        login: true,
        url: '/app-api/member/auth/weixin-mini-app-login',
        data: {
          phoneCode: phoneCode, // 手机 code
          loginCode: loginCode, // 登录 code
          state: 'default' // state
        },
        isRedirect: false, // 不重定向
        callBack: async (result) => {
          if (result.accessToken) {
            try {
              wx.setStorageSync('token', result.accessToken)
              wx.setStorageSync('refresh-token', result.refreshToken)
              wx.setStorageSync('userId', result.userId)
              wx.setStorageSync('openid', result.openid)
              getApp().globalData.loginStatus = 1
              getApp().globalData.userId = result.userId
              getApp().globalData.openid = result.openid

              // 查询用户信息
              const userInfo = await http.requestPromise({
                url: '/app-api/member/user/get',
                method: 'GET'
              })

              const groupId = userInfo.groupId
              getApp().globalData.userInfo = userInfo
              getApp().globalData.groupId = groupId
              wx.setStorageSync('userInfo', JSON.stringify(userInfo))
              wx.setStorageSync('groupId', groupId)

              if (groupId === 1) {
                // 家长信息
                try {
                  const studentParent = await http.requestPromise({
                    url: '/app-api/member/student-parent/get-by-user-id',
                    method: 'GET',
                    data: {
                      userId: result.userId
                    }
                  })
                  wx.setStorageSync('studentParent', JSON.stringify(studentParent))
                  getApp().globalData.studentParent = studentParent
                } catch (error) {
                  console.error('获取家长信息失败:', error)
                }
              }

              if (groupId === 2) {
                // 教师信息
                try {
                  const teacherInfo = await http.requestPromise({
                    url: '/app-api/member/teacher-info/get',
                    method: 'GET',
                    data: {
                      userId: result.userId
                    }
                  })
                  wx.setStorageSync('teacherInfo', JSON.stringify(teacherInfo))
                  getApp().globalData.teacherInfo = teacherInfo
                } catch (error) {
                  console.error('获取教师信息失败:', error)
                }
              }

              // 跳转到首页
              if (groupId === 1) {
                // 学生家长
                wx.reLaunch({
                  url: '/pages/index/index'
                })
              } else if (groupId === 2) {
                // 教师
                wx.reLaunch({
                  url: '/pages/index-teacher/index-teacher'
                })
              } else if (groupId === '' || groupId === null || groupId === undefined) {
                // 身份选择
                wx.reLaunch({
                  url: '/pages/createInfo/createInfo'
                })
              }
            } catch (error) {
              console.error('获取用户信息失败:', error)
              wx.showToast({
                title: '获取用户信息失败',
                icon: 'none'
              })
            }
          }
        }
      },
      true
    )
  }
})
