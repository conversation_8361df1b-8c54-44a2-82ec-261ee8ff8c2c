/*
 * @Author: 逗逗飞 <EMAIL>
 * @Date: 2025-07-02 16:45:30
 * @LastEditors: 逗逗飞 <EMAIL>
 * @LastEditTime: 2025-07-02 17:01:04
 * @FilePath: /mall4m-master/pages/pre-index/pre-index.js
 * @Description: 这个地方没有判断
 */
const http = require('../../utils/http.js')
Page({
  /**
   * 页面的初始数据
   */
  data: {},
  goTo() {
    wx.login({
      success: (res) => {
        getApp().globalData.code = res.code
        // 判断是否已经登录
        const token = wx.getStorageSync('token')
        if (!token) {
          wx.redirectTo({
            url: '/pages/login/login'
          })
          return false
        }
        getApp().globalData.loginStatus = 1
        // 获取用户信息
        this.getUserInfoAsync()
      }
    })
  },
  async getUserInfoAsync() {
    try {
      // 获取用户基本信息
      const userInfo = await http.requestPromise({
        url: '/app-api/member/user/get',
        method: 'GET'
      })

      // "groupId": 1 // 用户分组编号,1 学生家长 2 教师
      const groupId = userInfo.groupId || 0
      getApp().globalData.userInfo = userInfo
      getApp().globalData.groupId = groupId
      wx.setStorageSync('userInfo', JSON.stringify(userInfo))
      wx.setStorageSync('groupId', groupId)

      // 获取学生家长信息
      if (groupId === 1) {
        const studentParent = await http.requestPromise({
          url: '/app-api/member/student-parent/get-by-user-id',
          method: 'GET',
          data: {
            userId: wx.getStorageSync('userId')
          }
        })
        wx.setStorageSync('studentParent', JSON.stringify(studentParent))
        getApp().globalData.studentParent = studentParent
      } else if (groupId === 2) {
        const teacherInfo = await http.requestPromise({
          url: '/app-api/member/teacher-info/get',
          method: 'GET',
          data: {
            userId: wx.getStorageSync('userId')
          }
        })
        wx.setStorageSync('teacherInfo', JSON.stringify(teacherInfo))
        getApp().globalData.teacherInfo = teacherInfo
      }

      // 通知tabBar更新
      this.eventBus.emit('groupIdChange', groupId)

      // 跳转到首页
      if (groupId === 1) {
        // 学生家长
        wx.reLaunch({
          url: '/pages/index/index'
        })
      } else if (groupId === 2) {
        // 教师
        wx.reLaunch({
          url: '/pages/index-teacher/index-teacher'
        })
      } else {
        wx.reLaunch({
          url: '/pages/createInfo/createInfo'
        })
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
