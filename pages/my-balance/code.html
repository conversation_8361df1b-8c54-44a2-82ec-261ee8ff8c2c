<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钱包</title>
    <style>
        :root {
            --page-bg-color: #F7F7F7; /* Overall page background, very light gray */
            --card-bg-color: #FFFFFF;
            --primary-text-color: #000000;
            --secondary-text-color: #8A8A8E; /* iOS secondary label gray */
            --balance-header-gradient-start: #F0FAFF; /* Very subtle light blue */
            --balance-header-gradient-end: #F8FCFF;   /* Fading to almost white */
            --withdraw-button-bg-color: #60D9FF; /* Bright blue for button */
            --withdraw-button-text-color: #FFFFFF;
            --active-tab-indicator-color: #60D9FF; /* Matching blue for tab underline */
            --separator-color: #ECECEC; /* Light separator line, e.g., between transactions */
            --card-shadow: 0 1px 5px rgba(0, 0, 0, 0.06); /* Subtle shadow for cards */
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
            background-color: var(--page-bg-color);
            color: var(--primary-text-color);
            line-height: 1.4; /* Base line height */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .wallet-screen {
            /* This container wraps the whole content. 
               No max-width by default to allow it to be part of a larger layout.
               If it's a standalone page, you might add max-width & margin: auto. */
        }

        .balance-header {
            padding: 25px 20px; /* Top/bottom and left/right padding */
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(to bottom, var(--balance-header-gradient-start), var(--balance-header-gradient-end));
        }

        .balance-header .current-balance .label {
            font-size: 14px;
            color: var(--secondary-text-color);
            margin-bottom: 6px;
        }

        .balance-header .current-balance .amount {
            font-size: 42px; 
            color: var(--primary-text-color);
            font-weight: 500; /* Matched visual weight */
            line-height: 1.1;
        }

        .balance-header .withdraw-button {
            background-color: var(--withdraw-button-bg-color);
            color: var(--withdraw-button-text-color);
            border: none;
            padding: 10px 28px; 
            border-radius: 20px; /* Pill shape */
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(96, 217, 255, 0.3); /* Subtle shadow for the button */
        }

        .summary-card {
            background-color: var(--card-bg-color);
            margin: 15px 15px 0 15px; /* Top, LR margin, no bottom margin to abut next card if close */
            border-radius: 10px;
            display: flex;
            justify-content: space-around;
            padding: 18px 10px; /* Vertical and horizontal padding inside card */
            box-shadow: var(--card-shadow);
        }

        .summary-card .summary-item {
            text-align: center;
            flex: 1; /* Distribute space equally */
        }

        .summary-card .summary-item .label {
            font-size: 13px;
            color: var(--secondary-text-color);
            margin-bottom: 8px;
        }

        .summary-card .summary-item .amount {
            font-size: 20px;
            color: var(--primary-text-color);
            font-weight: 500;
        }

        .transactions-card {
            background-color: var(--card-bg-color);
            margin: 15px 15px 0 15px; /* Consistent margin with summary card, 0 bottom */
            /* If this is the absolute last element on the page, you might want margin-bottom: 15px; */
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            /* No bottom radius to match screenshot (implies continuation or specific end) */
            box-shadow: var(--card-shadow);
            overflow: hidden; /* Ensures content respects border radius, e.g. tab line */
        }

        .transactions-card .tabs {
            display: flex;
            position: relative; /* For the ::before pseudo-element underline */
        }

        .transactions-card .tabs::before { /* General gray underline for the whole tab bar */
            content: '';
            position: absolute;
            bottom: 0;
            left: 0; 
            right: 0;
            height: 1px;
            background-color: var(--separator-color);
            z-index: 0; /* Behind active tab indicator */
        }

        .transactions-card .tab {
            flex: 1;
            text-align: center;
            padding: 15px 0;
            font-size: 16px;
            color: var(--secondary-text-color);
            font-weight: 500;
            cursor: pointer;
            position: relative; /* For the ::after pseudo-element indicator */
            z-index: 1; /* Above the general underline */
        }

        .transactions-card .tab.active {
            color: var(--primary-text-color);
            font-weight: 600; /* Bolder for active tab text */
        }

        .transactions-card .tab.active::after { /* Blue indicator for active tab */
            content: '';
            position: absolute;
            bottom: 0px; /* Sits on the bottom edge of the tab */
            left: 50%;
            transform: translateX(-50%);
            width: 35px; /* Width of the indicator */
            height: 3px; /* Thickness of the indicator */
            background-color: var(--active-tab-indicator-color);
            border-radius: 1.5px; /* Rounded ends for the indicator */
        }

        .transaction-list {
            padding: 0 20px; /* Padding for transaction items within the list area */
        }

        .transaction-list .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 0; /* Vertical padding for each item */
            border-bottom: 1px solid var(--separator-color);
        }

        .transaction-list .transaction-item:last-child {
            border-bottom: none; /* No border for the last item */
        }

        .transaction-list .transaction-item .info .title {
            font-size: 16px;
            color: var(--primary-text-color);
            margin-bottom: 5px;
            font-weight: 500;
        }

        .transaction-list .transaction-item .info .subtitle {
            font-size: 13px;
            color: var(--secondary-text-color);
        }

        .transaction-list .transaction-item .details {
            text-align: right;
        }

        .transaction-list .transaction-item .details .amount {
            font-size: 20px; 
            color: var(--primary-text-color);
            font-weight: 600; /* Bold for amount */
            margin-bottom: 5px;
        }

        .transaction-list .transaction-item .details .date {
            font-size: 13px;
            color: var(--secondary-text-color);
        }

    </style>
</head>
<body>
    <div class="wallet-screen">
        <div class="balance-header">
            <div class="current-balance">
                <p class="label">当前余额(元)</p>
                <p class="amount">0.0</p>
            </div>
            <button class="withdraw-button">提现</button>
        </div>

        <div class="summary-card">
            <div class="summary-item">
                <p class="label">累计总收益(元)</p>
                <p class="amount">0.0</p>
            </div>
            <div class="summary-item">
                <p class="label">待到账余额</p>
                <p class="amount">0.0</p>
            </div>
            <div class="summary-item">
                <p class="label">已到账余额</p>
                <p class="amount">0.0</p>
            </div>
        </div>

        <div class="transactions-card">
            <div class="tabs">
                <div class="tab">已入账</div>
                <div class="tab active">待入账</div>
            </div>
            <div class="transaction-list">
                <div class="transaction-item">
                    <div class="info">
                        <p class="title">小学四年级数学</p>
                        <p class="subtitle">张三</p>
                    </div>
                    <div class="details">
                        <p class="amount">+230.24</p>
                        <p class="date">2025-02-15 22:20:25</p>
                    </div>
                </div>
                <div class="transaction-item">
                    <div class="info">
                        <p class="title">小学四年级数学</p>
                        <p class="subtitle">张三</p>
                    </div>
                    <div class="details">
                        <p class="amount">+230.24</p>
                        <p class="date">2025-02-15 22:20:25</p>
                    </div>
                </div>
                <div class="transaction-item">
                    <div class="info">
                        <p class="title">小学四年级数学</p>
                        <p class="subtitle">张三</p>
                    </div>
                    <div class="details">
                        <p class="amount">+230.24</p>
                        <p class="date">2025-02-15 22:20:25</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>