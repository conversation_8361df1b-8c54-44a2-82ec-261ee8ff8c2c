<!--
 * @Author: 逗逗飞 <EMAIL>
 * @Date: 2025-05-21 11:20:17
 * @LastEditors: 逗逗飞 <EMAIL>
 * @LastEditTime: 2025-05-25 16:15:56
 * @Description: 身份选择（选择家长 或者 老师）
-->
<view class="createInfo">
  <view class="title">请选择您的身份</view>
  <view class="subtitle">便于我们为您提供最佳服务</view>
  <view class="blueline"></view>
  <view>
    <view class="card {{ value == 1 ? 'chosedCard' : '' }}" bindtap="chosedCard" data-value="1">
      <checkbox checked="{{ value == 1 }}" value="1"></checkbox>
      <view class="cardInfo cardInfo-flex">
        <view class="cardInfo-text">
          <view class="cardTitle"> 我是学生家长 </view>
          <view class="cardSubTitle"> 言传身教，教育有方 </view>
        </view>
        <image class="cardInfo-img" src="./assets/icons/Frame.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="card {{ value == 2 ? 'chosedCard' : '' }}" bindtap="chosedCard" data-value="2">
      <checkbox checked="{{ value == 2 }}" value="2"></checkbox>
      <view class="cardInfo cardInfo-flex">
        <view class="cardInfo-text">
          <view class="cardTitle"> 我是老师 </view>
          <view class="cardSubTitle"> 教书育人，传道解惑 </view>
        </view>
        <image class="cardInfo-img" src="./assets/icons/Male.png" mode="aspectFit"></image>
      </view>
    </view>
    <button style="position: absolute" class="cardBtn" bind:tap="jumpTo"> 进入程序 </button>
  </view>
</view>
