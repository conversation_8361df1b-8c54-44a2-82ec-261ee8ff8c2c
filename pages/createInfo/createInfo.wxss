/* pages/createInfo/createInfo.wxss */
.createInfo {
  background: linear-gradient(180deg, #5cf5f680 0%, #ffffff 100%);
  height: 100vh;
  padding: 22rpx 60rpx;
  overflow: hidden;
}
.title {
  font-weight: 400;
  font-size: 48rpx;
}
.subtitle {
  font-size: 24rpx;
  margin: 20rpx 0;
}
.blueline {
  width: 60rpx;
  height: 6rpx;
  background: #49b9ba;
  border-radius: 3rpx 3rpx 3rpx 3rpx;
  margin-bottom: 80rpx;
}
.card {
  display: flex;
  align-items: center;
  width: 100%;
  height: 200rpx;
  background: linear-gradient(90deg, #ffffff 0%, #e6fffa 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  box-sizing: border-box;
  margin-bottom: 80rpx;
  position: relative;
}
.cardInfo {
  margin-left: 28rpx;
}
.cardTitle {
  font-size: 36rpx;
}
.cardSubTitle {
  font-size: 24rpx;
  margin-top: 16rpx;
  color: #666666;
}
.chosedCard {
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  border: 4rpx solid #49b9ba;
}
.cardInfo-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.cardInfo-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.cardInfo-img {
  width: 256rpx;
  height: 240rpx;
  margin-left: 24rpx;
  /* border-radius: 16rpx; */
  /* background: #e6fffa; */
  object-fit: contain;
  position: absolute;
  bottom: 0rpx;
  right: 0;
}
