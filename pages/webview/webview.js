/*
 * @Author: 逗逗飞 <EMAIL>
 * @Date: 2025-07-02 17:18:03
 * @LastEditors: 逗逗飞 <EMAIL>
 * @LastEditTime: 2025-07-02 17:32:32
 * @FilePath: /mall4m-master/pages/webview/webview.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// pages/webview/webview.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    targetSrc: 'https://mp.weixin.qq.com/'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const target = options.target.trim()
    switch (target) {
      case 'agreement': // 用户协议
        this.setData({
          targetSrc: 'https://onestopedu.xinjiajiao.top/legal/user_agreement.html'
        })
        break
      case 'policy': // 隐私政策
        this.setData({
          targetSrc: 'https://onestopedu.xinjiajiao.top/legal/privacy_policy.html'
        })
        break
      case 'disclaimer': // 《免责声明》
        this.setData({
          targetSrc: 'https://onestopedu.xinjiajiao.top/legal/disclaimer.html'
        })
        break
      default:
        break
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {}
})
