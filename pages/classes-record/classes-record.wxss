/* pages/container/container.wxss */
.weui-tabbar__icon {
  display: none;
}
.weui-tabbar {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  background: white;
  height: 86rpx;
}

.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label::after {
  content: '';
  width: 32rpx;
  height: 6rpx;
  background: #49b9ba;
  position: absolute;
  left: 0;
  transform: translateX(50%);
  bottom: -8rpx;
  border-radius: 30rpx;
}

.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label {
  font-size: 32rpx;
  color: black;
  font-weight: 500;
}

.weui-tabbar__item {
  padding: 0;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.weui-tabbar__label {
  position: relative;
  font-size: 28rpx;
}

.container {
  background: #f6f6f6;
  height: 100vh;
}
.main {
  width: 100%;
  background: linear-gradient(166deg, rgba(255, 255, 255, 0.57) 0%, #f6f6f6 100%);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  top: 86rpx;
  position: relative;
  padding: 34rpx 30rpx;
  box-sizing: border-box;
  margin-top: 28rpx;
}
