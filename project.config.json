{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": false, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": false, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "ignoreUploadUnusedFiles": true, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "localPlugins": false, "useCompilerPlugins": false, "condition": true, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.4.10", "appid": "wxd4125a48326c7f43", "projectname": "mall4j", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}