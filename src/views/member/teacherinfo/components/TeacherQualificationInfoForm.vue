<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100" />
       <el-table-column label="资质标签" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.qualificationDesc`" :rules="formRules.qualificationDesc" class="mb-0px!">
            <el-input v-model="row.qualificationDesc" placeholder="请输入资质标签" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="资质文件路径" min-width="150">
        <template #default="{ row, $index }">
          <UploadImg :prop="`${$index}.filePath`" v-model="row.filePath" />
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加老师标签</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { TeacherInfoApi } from '@/api/member/teacherinfo'

const props = defineProps<{
  teacherId: undefined // 老师编号（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref([])
const formRules = reactive({
  teacherId: [{ required: true, message: '老师编号不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.teacherId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await TeacherInfoApi.getTeacherQualificationInfoListByTeacherId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    teacherId: undefined,
    qualificationDesc: undefined,
    filePath: undefined,
  }
  row.teacherId = props.teacherId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>
