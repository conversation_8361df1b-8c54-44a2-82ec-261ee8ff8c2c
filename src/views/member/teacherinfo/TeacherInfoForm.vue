<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.MEMBER_TEACHER_STATUS)"
              :key="dict.value"
              :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="auditRemark">
        <el-input v-model="formData.auditRemark" placeholder="请输入审核意见" />
      </el-form-item>
      <el-form-item label="单价（元/课时）" prop="unitPrice">
        <el-input v-model="formData.unitPrice" placeholder="请输入单价（元/课时）" />
      </el-form-item>
      <el-form-item label="教师提成比例（%）" prop="incomeRate">
        <el-input v-model="formData.incomeRate" placeholder="请输入提成比例，如：78，表示提成78%" />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="行政区划" prop="areaCode">
        <el-input v-model="formData.areaCode" placeholder="请输入行政区划" />
      </el-form-item>
      <el-form-item label="地址信息-到小区" prop="addressArea">
        <el-input v-model="formData.addressArea" placeholder="请输入地址信息-到小区" />
      </el-form-item>
      <el-form-item label="地址信息-到具体门牌号" prop="addressSpecific">
        <el-input v-model="formData.addressSpecific" placeholder="请输入地址信息-到具体门牌号" />
      </el-form-item>
      <el-form-item label="真实名字" prop="name">
        <el-input v-model="formData.name" placeholder="请输入真实名字" />
      </el-form-item>
      <el-form-item label="用户性别" prop="sex">
        <el-radio-group v-model="formData.sex">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="出生日期" prop="birthday">
        <el-input v-model="formData.birthday" placeholder="请输入出生日期" />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input v-model="formData.idCard" placeholder="请输入身份证号" />
      </el-form-item>
      <el-form-item label="简介" prop="teacherProfile">
        <el-input v-model="formData.teacherProfile" type="textarea"  placeholder="请输入简介"/>
      </el-form-item>
      <el-form-item label="专业" prop="specialSubject">
        <el-input v-model="formData.specialSubject" placeholder="请输入专业" />
      </el-form-item>
      <el-form-item label="在读学校/毕业院校" prop="school">
        <el-input v-model="formData.school" placeholder="请输入在读学校/毕业院校" />
      </el-form-item>
      <el-form-item label="学历" prop="qualification">
        <el-radio-group v-model="formData.qualification">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.MEMBER_EDUCATION_LEVEL)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="籍贯" prop="nativePlace">
        <el-input v-model="formData.nativePlace" placeholder="请输入籍贯" />
      </el-form-item>
      <el-form-item label="头像" prop="headPicture">
        <UploadImg v-model="formData.headPicture" />
      </el-form-item>
      <el-form-item label="教育阶段" prop="stage">
        <el-radio-group v-model="formData.stage">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.MEMBER_STUDENT_EDUCATION_STAGE)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="科目ID,逗号分割" prop="subjectName">
        <el-input v-model="formData.subjectName"  />
      </el-form-item>
      <el-form-item label="身份证正面" prop="idCardFace">
        <UploadImg v-model="formData.idCardFace" />
      </el-form-item>
      <el-form-item label="身份证背面" prop="idCardBack">
        <UploadImg v-model="formData.idCardBack" />
      </el-form-item>
      <el-form-item label="毕业证/学生证" prop="diplomaFilePath">
        <UploadImg v-model="formData.diplomaFilePath" />
      </el-form-item>
      <el-form-item label="资质证书" prop="certificatePath">
        <UploadImg v-model="formData.certificatePath" />
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="老师教育经历" name="teacherExperienceInfo">
        <TeacherExperienceInfoForm ref="teacherExperienceInfoFormRef" :teacher-id="formData.userId" />
      </el-tab-pane>
      <el-tab-pane label="老师标签" name="teacherQualificationInfo">
        <TeacherQualificationInfoForm ref="teacherQualificationInfoFormRef" :teacher-id="formData.userId" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TeacherInfoApi, TeacherInfoVO } from '@/api/member/teacherinfo'
import TeacherExperienceInfoForm from './components/TeacherExperienceInfoForm.vue'
import TeacherQualificationInfoForm from './components/TeacherQualificationInfoForm.vue'

/** 教师信息 表单 */
defineOptions({ name: 'TeacherInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改,view-查看不做提交
const formData = ref({
  id: undefined,
  mobile: undefined,
  userId: undefined,
  auditRemark: undefined,
  status: undefined,
  areaCode: undefined,
  addressArea: undefined,
  addressSpecific: undefined,
  name: undefined,
  sex: undefined,
  birthday: undefined,
  idCard: undefined,
  teacherProfile: undefined,
  specialSubject: undefined,
  school: undefined,
  qualification: undefined,
  mark: undefined,
  unitPrice: undefined,
  levelId: undefined,
  pointLon: undefined,
  pointLat: undefined,
  nativePlace: undefined,
  headPicture: undefined,
  stage: undefined,
  subjectId: undefined,
  subjectName: undefined,
  idCardFace: undefined,
  idCardBack: undefined,
  diplomaFilePath: undefined,
  certificatePath: undefined,
})
const formRules = reactive({
  status: [{ required: true, message: '状态（0-申请中，1-审核通过，2-审核不通过）不能为空', trigger: 'blur' }],
  addressArea: [{ required: true, message: '地址信息-到小区不能为空', trigger: 'blur' }],
  addressSpecific: [{ required: true, message: '地址信息-到具体门牌号不能为空', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '单价（元/课时）不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('teacherExperienceInfo')
const teacherExperienceInfoFormRef = ref()
const teacherQualificationInfoFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true

  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      var data = await TeacherInfoApi.getTeacherInfo(id)
      if (data && data.subjectName && Array.isArray(data.subjectName)) {
        data.subjectName = data.subjectName.join(',')
      }
      formData.value = data
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await teacherExperienceInfoFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'teacherExperienceInfo'
    return
  }
  try {
    await teacherQualificationInfoFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'teacherQualificationInfo'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherInfoVO
    // 拼接子表的数据
    data.teacherExperienceInfos = teacherExperienceInfoFormRef.value.getData()
    data.teacherQualificationInfos = teacherQualificationInfoFormRef.value.getData()
    // if (formType.value === 'create') {
    //   await TeacherInfoApi.createTeacherInfo(data)
    //   message.success(t('common.createSuccess'))
    // } else if (formType.value === 'update') {
    //   await TeacherInfoApi.updateTeacherInfo(data)
    //   message.success(t('common.updateSuccess'))
    // }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    mobile: undefined,
    userId: undefined,
    auditRemark: undefined,
    status: undefined,
    areaCode: undefined,
    addressArea: undefined,
    addressSpecific: undefined,
    name: undefined,
    sex: undefined,
    birthday: undefined,
    idCard: undefined,
    teacherProfile: undefined,
    specialSubject: undefined,
    school: undefined,
    qualification: undefined,
    mark: undefined,
    unitPrice: undefined,
    levelId: undefined,
    pointLon: undefined,
    pointLat: undefined,
    nativePlace: undefined,
    headPicture: undefined,
    stage: undefined,
    subjectId: undefined,
    idCardFace: undefined,
    idCardBack: undefined,
    diplomaFilePath: undefined,
    certificatePath: undefined,
  }
  formRef.value?.resetFields()
}
</script>
