<template>
    <div class="container">
        <div>
            <!-- 搜索 -->
            <div class="search">
                <el-form
                    :model="searchForm"
                    label-width="80px"
                    style="display: flex;"
                >
                    <el-form-item label="机构名称" prop="agencyName">
                        <el-input v-model="searchForm.agencyName" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label-width="20px">
                        <el-button type="primary" icon="el-icon-search" @click="search" class="search mb15">搜索</el-button>
                    </el-form-item>
                    <div class="add fr mb15" @click="added" title="新增" style="margin: 10px 20px 10px auto;">
                        <span>+</span>
                    </div>
                </el-form>
            </div>
            
            <!-- 列表 -->
            <div class="table">
                <el-table :data="tableData" style="width: 100%;margin-bottom: 20px;" border>
                    <el-table-column prop="agencyName" label="机构名称"  fixed="left" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="associatedExperts" label="合作专家" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="businessLicenseNumber" label="营业执照编号" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="contactInfo" label="联系方式" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="legalRepresentative" label="法人代表" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="qualificationCertificates" label="资质证书" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="qualificationLevel" label="资质等级" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="registeredAddress" label="注册地址" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="representativeProjects" label="代表性项目" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="status" label="状态" align="center" min-width="160">
                        <template slot-scope="scope">
                            <div v-if="scope.row.status == 0">待审核</div>
                            <div v-else>
                                <el-switch
                                    style="display: block"
                                    :value="scope.row.status"
                                    :active-value="1"
                                    :inactive-value="2"
                                    active-color="#13ce66"
                                    inactive-color="#ff4949"
                                    active-text="正常"
                                    inactive-text="停用"
                                    @change="changeStatus($event,scope)">
                                </el-switch>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="reviewArea" label="评审领域" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="serviceScope" label="工作范围" align="center" min-width="160"></el-table-column>
                    <el-table-column prop="remarks" label="备注" align="center" min-width="160"></el-table-column>
                    <el-table-column label="操作" width="200" fixed="right" align="center">
                        <template slot-scope="scope">
                            <el-button type="text" size="mini" @click="edit(scope.row)">编辑</el-button>
                            <el-button type="text" size="mini" @click="remove(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 分页 -->
            <el-pagination
                slot="footer"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pagination.page"
                :page-sizes="pagination.pageSizes"
                :page-size="pagination.size"
                layout="total, sizes, prev, pager, next"
                :total="pagination.total"
            ></el-pagination>

            <!-- 弹框 -->
            <el-dialog
                :title="dialog.title"
                :visible.sync="dialog.visible"
                width="1183px"
                :close-on-click-modal="false"
            >
                <el-form
                    ref="dialogFormRef"
                    :rules="rules"
                    :model="dialogForm"
                    label-width="120px"
                    class="addForm"
                >
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="机构名称：" prop="agencyName">
                                <el-input v-model="dialogForm.agencyName" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="合作专家：" prop="associatedExperts">
                                <el-input v-model="dialogForm.associatedExperts" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="评审领域：" prop="reviewArea">
                                <el-input v-model="dialogForm.reviewArea" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="联系方式：" prop="contactInfo">
                                <el-input v-model="dialogForm.contactInfo" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="法人代表：" prop="legalRepresentative">
                                <el-input v-model="dialogForm.legalRepresentative" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="工作范围：" prop="serviceScope">
                                <el-input v-model="dialogForm.serviceScope" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="资质证书：" prop="qualificationCertificates">
                                <el-input v-model="dialogForm.qualificationCertificates" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="资质等级：" prop="qualificationLevel">
                                <el-input v-model="dialogForm.qualificationLevel" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="注册地址：" prop="registeredAddress">
                                <el-input v-model="dialogForm.registeredAddress" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="注册资本：" prop="registeredCapital">
                                <el-input v-model="dialogForm.registeredCapital" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="代表性项目：" prop="representativeProjects">
                                <el-input v-model="dialogForm.representativeProjects" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="营业执照编号:" prop="businessLicenseNumber">
                                <el-input v-model="dialogForm.businessLicenseNumber" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- <el-row>
                        <el-col :span="11">
                            
                            <el-form-item label="系统操作用户：" prop="operator">
                                <el-input v-model="dialogForm.operator" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="状态：" prop="status">
                                <el-select  v-model="dialogForm.status"  size="small" style="width: 100%;">
                                    <el-option label="待审核" :value="0"></el-option>
                                    <el-option label="正常" :value="1"></el-option>
                                    <el-option label="停用" :value="2"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row> -->
                    <el-row>
                        <el-col :span="23" >
                            <el-form-item label="备注：" prop="remarks">
                                <el-input v-model="dialogForm.remarks" type="textarea" size="small"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="10">
                        <el-col :span="24" class="addBtn">
                            <el-button type="primary" @click="save">保存</el-button>
                            <el-button @click="closeDialog" class="backC">取消</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </el-dialog>
            
        </div>
    </div>
</template>

<script>
import { 
    handleAdd,
    getList,
    handleDel,
    handleEdit
}from '@/api/governmentSubsystem/integratedManage/Third-partyReviewBody.js'
export default {
    props:{},
    data(){
        return  {
            searchForm:{

            },
            tableData:[{}],
            pagination:{
                pageSizes: [10, 20, 50, 100], // 默认分页可选择的每页显示的页数
                size: 10, // 分页每页默认显示10条
                page: 1, // 当前默认第一页
                total: 0 // 总条数
            },
            dialog:{
                title:'',
                visible:false,
                type:'add',
            },
            dialogForm:{
                id:''
            },
            rules:{
                agencyName:[
                    { required: true, message: "请输入机构名称", trigger: ["blur", "change"]}
                ],
                associatedExperts:[
                    { required: true, message: "请输入合作专家", trigger: ["blur", "change"]}
                ],
                reviewArea:[
                    { required: true, message: "请输入评审领域", trigger: ["blur", "change"]}
                ],
            },
        }
    },
    components:{},
    created(){},
    mounted(){
        this.search()
    },
    methods:{
        search(){
            this.pagination.page = 1;
            this.getList()
        },
        getList(){
            let data = Object.assign({},this.searchForm)
            data.pageNum = this.pagination.page
            data.pageSize = this.pagination.size
            getList(data).then(res => {
                this.tableData = res.data.list
                this.pagination.total = res.data.total
            })
        },
        //分页
        handleSizeChange(val) {
            this.pagination.size = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.page = val;
            this.getList();
        },
        added(){
            this.dialog.visible = true
            this.dialog.title = '新增'
            this.dialog.type = 'add'
        },
        edit(row){
            this.dialogForm = Object.assign({},row)
            this.dialog.visible = true
            this.dialog.title = '编辑' 
            this.dialog.type = 'edit'
        },
        remove(row){
            this.$confirm("确认删除吗？删除后不可恢复", "删除", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                handleDel(row.id).then(res => {
                if (res.code == 200) {
                    this.$message({
                    type: "success",
                    message: "删除成功!"
                    });
                    this.getList();
                } else {
                    this.$message.error(res.message);
                }
                });
            });
        },


        /* 弹框事件 */
        save(){
            this.$refs.dialogFormRef.validate((valid) => {
                if(valid){
                    let submitApi = this.dialog.type === 'add' ? handleAdd : handleEdit
                    if(this.dialog.type === 'add'){
                        delete this.dialogForm.id
                        this.dialogForm.status = 1
                    }
                    delete this.dialogForm.updateTime
                    delete this.dialogForm.createTime
                    submitApi(this.dialogForm).then(res => {
                        // this.dialog.visible = false
                        this.closeDialog()
                        this.$message.success('操作成功')
                        this.search()
                    })
                }
            })
        },
        closeDialog(){
            this.dialogForm = this.emptyObject(this.dialogForm)
            this.$nextTick(() => {
                this.$refs.dialogFormRef.clearValidate()
                this.dialog.visible = false
            })
        },
        changeStatus(val,row){
            // console.log('改变后的状态');
            // console.log(val);
            // console.log(row);
            let data = Object.assign({},row.row)
            data.status = val
            handleEdit(data).then(res => {
                this.$set(this.tableData[row.$index],'status',val)
                this.getList()
            })
        },
    },
    beforeDestroy(){},
}
</script>

<style lang="scss" scoped>
</style>